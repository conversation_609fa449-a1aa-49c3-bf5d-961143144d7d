# Task ID: 30
# Title: Remove TenantEntity Interface from Department Entity
# Status: pending
# Dependencies: None
# Priority: high
# Description: Clean up the Department entity by removing TenantEntity interface implementation while keeping the tenantId field for manual handling.
# Details:
Update src/modules/hrm/org-units/entities/department.entity.ts to remove TenantEntity interface implementation. Keep the tenantId field and remove the import statement for TenantEntity.

# Test Strategy:
Verify that the entity compiles without errors and tenantId field is still present.
